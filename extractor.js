const puppeteer = require("puppeteer");
const fs = require("fs").promises;
const path = require("path");
const { PDFDocument } = require("pdf-lib");

// --- Configuration ---
const PHONE_WIDTH = 375;
const DEFAULT_PC_WIDTH = 1024;
const CAPTURE_HEIGHT_PX = 10000; // Fixed large height for capture
const DELAY_MS = 3000; // Delay between page captures
const PAGE_LOAD_CHECK_TIMEOUT = 10000;
const TEMP_OUTPUT_DIR = "temp output";

// --- Helper Functions ---
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

async function safeNavigate(page, url, sendStatus, retries = 3) {
  sendStatus(`[Navigate] Starting navigation attempts to ${url}`);
  for (let attempt = 1; attempt <= retries; attempt++) {
    sendStatus(`[Navigate] Attempt ${attempt}/${retries}...`);
    try {
      await page.goto(url, { waitUntil: "networkidle0", timeout: 90000 });
      sendStatus(`[Navigate] SUCCESS: Navigated to ${url}`);
      return;
    } catch (error) {
      sendStatus(`[Navigate] WARN: Attempt ${attempt} failed: ${error.message}`);
      if (attempt === retries) {
        sendStatus(
          `[Navigate] FATAL: Failed to navigate after ${retries} attempts. Is the local server running correctly at ${url}?`,
        );
        throw error;
      }
      const retryDelaySec = DELAY_MS / 1000;
      sendStatus(`[Navigate] Retrying in ${retryDelaySec}s...`);
      await delay(DELAY_MS);
    }
  }
}

// --- Main Extraction Function (Website Capture) ---
async function runExtraction(options, sendStatus) {
  const { url, outputFile, mode, viewportWidth, viewportHeight, quality, signal, pageConfig } = options;

  // Determine max pages based on page configuration
  let maxPages = 500; // default
  if (pageConfig) {
    if (pageConfig.type === "all") {
      maxPages = pageConfig.maxPages || 500;
    } else if (pageConfig.type === "range") {
      maxPages = (pageConfig.endPage - pageConfig.startPage + 1);
    } else if (pageConfig.type === "single") {
      maxPages = 1;
    }
  }

  const serverUrl = url || "http://localhost:8000";

  sendStatus(
    `[Init] Starting PDF extraction | Mode: ${mode} | Output: ${path.basename(outputFile)}`
  );
  sendStatus(`[Init] Target URL: ${url}`);
  sendStatus(`[Init] Using viewport size: ${viewportWidth || 'default'}x${viewportHeight || 'auto'}`);
  sendStatus(`[Init] Quality setting: ${quality || 'default'}`);
  sendStatus(`[Init] Temporary PDFs will be saved to: ${TEMP_OUTPUT_DIR}/`);

  let browser = null;
  const pdfBuffers = [];
  const tempOutputDirPath = path.join(__dirname, TEMP_OUTPUT_DIR);

  try {
    // --- Create Temporary Output Directory ---
    try {
      await fs.mkdir(tempOutputDirPath, { recursive: true });
      sendStatus(`[Init] Ensured temporary directory exists: ${tempOutputDirPath}`);
    } catch (dirError) {
      sendStatus(
        `[Init] FATAL: Could not create temporary directory: ${tempOutputDirPath}. Error: ${dirError.message}`,
      );
      throw dirError;
    }

    // --- Launch Headless Chromium ---
    sendStatus("[Launch] Launching headless Chromium browser instance...");
    browser = await puppeteer.launch({
      headless: "new",
      slowMo: 50,
      defaultViewport: null, // Start without fixed viewport
      args: [
        "--font-render-hinting=none",
        "--disable-smooth-scrolling", // Keep just in case
        // Remove complex rendering args, might not be needed
      ],
    });
    sendStatus(`[Launch] SUCCESS: Headless Chromium launched.`);

    const page = await browser.newPage();
    sendStatus(`[Page] New page created.`);
    page.on("console", (msg) => sendStatus(`[Page Console] ${msg.text()}`));
    page.on("pageerror", (err) => sendStatus(`[Page Error] ${err.message}`));

    // --- Navigate to the Local Server URL ---
    await safeNavigate(page, serverUrl, sendStatus);

    sendStatus(`[Delay] Initial load delay (${DELAY_MS / 1000}s)...`);
    await delay(DELAY_MS);
    sendStatus(`[Delay] Initial load delay finished.`);

    // --- PDF Capture Loop ---
    let pageNumber = 0;
    sendStatus("[Loop] Starting page capture loop...");
    for (pageNumber = 1; pageNumber <= maxPages; pageNumber++) {
      if (signal.aborted) {
        sendStatus(
          `[Loop] Received stop signal. Aborting capture at page ${pageNumber}.`,
        );
        break;
      }

      sendStatus(`\n--- [Loop] Processing Page ${pageNumber}/${maxPages} ---`);
      try {        // Set viewport based on mode and options
        const width = mode === "phone" ? PHONE_WIDTH :
                     mode === "custom" ? viewportWidth :
                     DEFAULT_PC_WIDTH;
        const height = mode === "custom" ? viewportHeight : CAPTURE_HEIGHT_PX;

        sendStatus(`[Page] Setting viewport: ${width}x${height}`);
        await page.setViewport({ width, height });
        // Optional: Scroll to top before capture (might help ensure consistency)
        await page.evaluate(() => window.scrollTo(0, 0));
        await delay(500); // Wait briefly for viewport change and scroll        // Capture PDF with specified settings
        sendStatus(`[Page] Capturing PDF...`);
        const pdfOptions = {
            width: mode === "custom" ? `${width}px` : undefined,
            height: mode === "custom" ? `${height}px` : undefined,
            printBackground: true,
            preferCSSPageSize: !mode === "custom",
            margin: { top: "20px", right: "20px", bottom: "20px", left: "20px" },
            timeout: 180000,
            scale: quality === "print" ? 2 : 1,
            format: mode === "custom" ? undefined : "A4"
        };
        const pdfBuffer = await page.pdf(pdfOptions);
        pdfBuffers.push(pdfBuffer);
        sendStatus(
          `[Page ${pageNumber}] SUCCESS: PDF captured (${(
            pdfBuffer.length / 1024
          ).toFixed(1)} KB).`,
        );

        // *** 3. Save Temporary PDF ***
        const tempPdfFileName = `temp_page_${pageNumber}.pdf`;
        const tempPdfPath = path.join(tempOutputDirPath, tempPdfFileName);
        sendStatus(
          `[Page ${pageNumber}] Saving temporary PDF to: ${tempPdfPath}...`,
        );
        try {
          await fs.writeFile(tempPdfPath, pdfBuffer);
          sendStatus(
            `[Page ${pageNumber}] SUCCESS: Saved temporary PDF: ${tempPdfFileName}`,
          );
        } catch (tempSaveError) {
          sendStatus(
            `[Page ${pageNumber}] WARN: Failed to save temporary PDF ${tempPdfFileName}. Error: ${tempSaveError.message}`,
          );
          console.warn(
            `[Page ${pageNumber}] Temporary save error details:`,
            tempSaveError,
          );
        }

        // *** 4. Navigate to Next Page ***
        if (pageNumber < maxPages) {
          if (signal.aborted) {
            sendStatus(`[Loop] Stop signal received before navigation.`);
            break;
          }
          sendStatus(
            `[Page ${pageNumber}] Simulating 'ArrowRight' key press for next page...`,
          );
          await page.focus("body"); // Focus main body for key press
          await page.keyboard.press("ArrowRight");
          sendStatus(
            `[Page ${pageNumber}] Key press sent. Waiting for network idle/delay...`,
          );
          try {
            await page.waitForNetworkIdle({
              idleTime: 1000,
              timeout: PAGE_LOAD_CHECK_TIMEOUT,
            });
            sendStatus(
              `[Page ${pageNumber}] Network idle detected after key press.`,
            );
          } catch (navError) {
            if (signal.aborted) {
              sendStatus(`[Loop] Stop signal received during navigation/wait.`);
              break;
            }
            sendStatus(
              `[Page ${pageNumber}] WARN: Navigation/idle wait failed after key press: ${navError.message}. Proceeding anyway.`,
            );
          }
          if (signal.aborted) {
            sendStatus(`[Loop] Stop signal received before final delay.`);
            break;
          }
          await delay(DELAY_MS); // Wait after navigation
          sendStatus(`[Page ${pageNumber}] Page transition delay finished.`);
        } else {
          sendStatus(
            `[Loop] Reached Max Pages limit (${maxPages}). Stopping capture.`,
          );
        }
      } catch (pageError) {
        if (signal.aborted) {
          sendStatus(`[Page ${pageNumber}] Loop aborted during page processing.`);
          break;
        }
        sendStatus(`[Page ${pageNumber}] ERROR: ${pageError.message}`);
        console.error(`[Page ${pageNumber}] Error details:`, pageError);
        sendStatus(`[Loop] Stopping loop due to error.`);
        break; // Stop loop on error
      }
    } // End for loop

    // ... (rest of the function: loop finished message, merging, cleanup) ...
    if (pageNumber >= maxPages && !signal.aborted) {
      sendStatus(
        `[Loop] Finished capture after reaching Max Pages limit (${maxPages}).`,
      );
    }
    sendStatus("[Loop] Page capture loop finished.");

    // --- PDF Merging ---
    if (pdfBuffers.length > 0) {
      sendStatus(
        `\n[Merge] Starting merge for ${pdfBuffers.length} captured PDF pages...`,
      );
      const mergedPdf = await PDFDocument.create();
      let mergedCount = 0;
      for (let i = 0; i < pdfBuffers.length; i++) {
        const pageNum = i + 1;
        sendStatus(
          `[Merge] Processing PDF buffer ${pageNum}/${pdfBuffers.length}...`,
        );
        try {
          const pdfDoc = await PDFDocument.load(pdfBuffers[i]);
          const indices = pdfDoc.getPageIndices();
          sendStatus(
            `[Merge] Buffer ${pageNum}: Found ${indices.length} page(s). Copying...`,
          );
          const copiedPages = await mergedPdf.copyPages(pdfDoc, indices);
          copiedPages.forEach((page) => {
            mergedPdf.addPage(page);
          });
          mergedCount++;
          sendStatus(`[Merge] Buffer ${pageNum} merged successfully.`);
        } catch (mergeError) {
          sendStatus(
            `[Merge] WARN: Skipping PDF buffer ${pageNum} due to merging error: ${mergeError.message}`,
          );
          console.warn(`[Merge] Merge error on buffer ${pageNum}:`, mergeError);
        }
      }
      if (mergedCount > 0) {
        sendStatus(
          `[Merge] Saving ${mergedCount} merged pages to ${outputFile}...`,
        );
        const pdfBytes = await mergedPdf.save();
        await fs.writeFile(outputFile, pdfBytes);
        sendStatus(
          `[Merge] SUCCESS: Saved merged PDF (${(
            pdfBytes.length /
            1024 /
            1024
          ).toFixed(2)} MB)`,
        );
      } else {
        sendStatus("[Merge] ERROR: No PDF pages could be successfully merged.");
        throw new Error("PDF merging failed for all captured pages.");
      }
    } else {
      sendStatus("[Merge] WARN: No PDF pages were captured to merge.");
    }
  } catch (error) {
    if (signal && signal.aborted) {
      console.log("[Extractor] Extraction aborted by signal.");
    } else {
      console.error("[Extractor] FATAL ERROR:", error);
      sendStatus(`[Extractor] FATAL ERROR: ${error.message}`);
      throw error;
    }
  } finally {
    // ... (cleanup remains the same) ...
    sendStatus("[Cleanup] Entering finally block...");
    if (browser) {
      sendStatus("[Cleanup] Chromium instance exists. Attempting to close...");
      try {
        await browser.close();
        sendStatus("[Cleanup] Chromium browser closed successfully.");
      } catch (closeError) {
        sendStatus(
          `[Cleanup] WARN: Error closing Chromium browser: ${closeError.message}`,
        );
        console.error("[Cleanup] Browser close error:", closeError);
      }
    } else {
      sendStatus("[Cleanup] No browser instance to close.");
    }
    sendStatus("[Cleanup] Extraction function finished.");
  }
}

module.exports = runExtraction;
