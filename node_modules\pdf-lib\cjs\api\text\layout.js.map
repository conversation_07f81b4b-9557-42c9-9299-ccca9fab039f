{"version": 3, "file": "layout.js", "sourceRoot": "", "sources": ["../../../src/api/text/layout.ts"], "names": [], "mappings": ";;;AACA,oCAAuD;AACvD,yCAAuD;AAGvD,qCAMmB;AAkBnB,IAAM,aAAa,GAAG,CAAC,CAAC;AACxB,IAAM,aAAa,GAAG,GAAG,CAAC;AAE1B,IAAM,eAAe,GAAG,UACtB,KAAe,EACf,IAAa,EACb,MAAoB,EACpB,SAA0B;IAA1B,0BAAA,EAAA,iBAA0B;IAE1B,IAAI,QAAQ,GAAG,aAAa,CAAC;IAE7B,OAAO,QAAQ,GAAG,aAAa,EAAE;QAC/B,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KACE,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,MAAM,EACvC,OAAO,GAAG,OAAO,EACjB,OAAO,EAAE,EACT;YACA,SAAS,IAAI,CAAC,CAAC;YAEf,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE9B,+DAA+D;YAC/D,iDAAiD;YACjD,IAAI,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAC;YACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACtD,IAAM,UAAU,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;gBACnC,IAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;gBACxD,IAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAC3D,oBAAoB,IAAI,WAAW,CAAC;gBACpC,IAAI,oBAAoB,IAAI,CAAC,EAAE;oBAC7B,SAAS,IAAI,CAAC,CAAC;oBACf,oBAAoB,GAAG,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC;iBACnD;aACF;SACF;QAED,0CAA0C;QAC1C,IAAI,CAAC,SAAS,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM;YAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;QAEhE,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAM,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC;QACzC,IAAM,WAAW,GAAG,UAAU,GAAG,SAAS,CAAC;QAE3C,2CAA2C;QAC3C,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;YAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;QAE/D,QAAQ,IAAI,CAAC,CAAC;KACf;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF,IAAM,qBAAqB,GAAG,UAC5B,IAAY,EACZ,IAAa,EACb,MAAoB,EACpB,SAAiB;IAEjB,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;IAC3C,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IAEjC,IAAI,QAAQ,GAAG,aAAa,CAAC;IAE7B,IAAM,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,OAAO,QAAQ,GAAG,aAAa,EAAE;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACtD,IAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACrB,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;YACvE,IAAI,OAAO;gBAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;SAClC;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QACjE,IAAI,MAAM,GAAG,UAAU;YAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;QAE7C,QAAQ,IAAI,CAAC,CAAC;KACf;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAgBF,IAAM,qBAAqB,GAAG,UAAC,IAAY;IACzC,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;QAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAAE,OAAO,GAAG,CAAC;KACtC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,IAAM,aAAa,GAAG,UACpB,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,QAAgB;;IAEhB,IAAI,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC;IACrC,OAAO,iBAAiB,GAAG,CAAC,EAAE;QAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;QACnD,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,GAAG,QAAQ,EAAE;YACpB,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC;YAClE,OAAO,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE,SAAS,WAAA,EAAE,CAAC;SAC5C;QACD,iBAAiB,SAAG,qBAAqB,CAAC,IAAI,CAAC,mCAAI,CAAC,CAAC;KACtD;IAED,yEAAyE;IACzE,kEAAkE;IAClE,OAAO;QACL,IAAI,EAAE,KAAK;QACX,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAC/B,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC;QAC9C,SAAS,EAAE,SAAS;KACrB,CAAC;AACJ,CAAC,CAAC;AAEW,QAAA,mBAAmB,GAAG,UACjC,IAAY,EACZ,EAAwD;QAAtD,SAAS,eAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAA,EAAE,MAAM,YAAA;IAEnC,IAAM,KAAK,GAAG,iBAAS,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzC,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC5C,QAAQ,GAAG,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KACvD;IACD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC3C,IAAM,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC;IAEzC,IAAM,SAAS,GAAmB,EAAE,CAAC;IAErC,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IACpB,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IACpB,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;IACnC,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAEpC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACjC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAI,aAAa,GAAuB,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,OAAO,aAAa,KAAK,SAAS,EAAE;YAC5B,IAAA,KAAsC,aAAa,CACvD,aAAa,EACb,MAAM,CAAC,KAAK,EACZ,IAAI,EACJ,QAAQ,CACT,EALO,IAAI,UAAA,EAAE,OAAO,aAAA,EAAE,KAAK,WAAA,EAAE,SAAS,eAKtC,CAAC;YAEF,kBAAkB;YAClB,IAAM,CAAC,GAAG,CACN,SAAS,KAAK,yBAAa,CAAC,IAAI,CAAG,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC/C,CAAC,CAAC,SAAS,KAAK,yBAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;oBAClF,CAAC,CAAC,SAAS,KAAK,yBAAa,CAAC,KAAK,CAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK;wBACtE,CAAC,CAAC,MAAM,CAAC,CAAC,CACX,CAAC;YAEF,CAAC,IAAI,UAAU,CAAC;YAEhB,IAAI,CAAC,GAAG,IAAI;gBAAE,IAAI,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,GAAG,IAAI;gBAAE,IAAI,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;gBAAE,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;YACvC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI;gBAAE,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC;YAEzC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;YAE7D,yEAAyE;YACzE,qDAAqD;YACrD,aAAa,GAAG,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,EAAE,CAAC;SACnC;KACF;IAED,OAAO;QACL,QAAQ,UAAA;QACR,UAAU,YAAA;QACV,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE;YACN,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,IAAI;YACP,KAAK,EAAE,IAAI,GAAG,IAAI;YAClB,MAAM,EAAE,IAAI,GAAG,IAAI;SACpB;KACF,CAAC;AACJ,CAAC,CAAC;AAeW,QAAA,gBAAgB,GAAG,UAC9B,IAAY,EACZ,EAA8D;QAA5D,QAAQ,cAAA,EAAE,IAAI,UAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAA;IAEnC,IAAM,IAAI,GAAG,kBAAU,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzC,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;QAC3B,MAAM,IAAI,8BAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;KACzD;IAED,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC5C,QAAQ,GAAG,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;KACjE;IAED,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;IAE3C,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;IACjE,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAEtD,IAAM,KAAK,GAAmB,EAAE,CAAC;IAEjC,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IACpB,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IACpB,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;IACnC,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAEpC,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,OAAO,UAAU,GAAG,SAAS,EAAE;QACvB,IAAA,KAAqB,mBAAW,CAAC,IAAI,EAAE,UAAU,CAAC,EAAjD,IAAI,QAAA,EAAE,UAAU,QAAiC,CAAC;QAEzD,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAErD,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QACvE,IAAM,CAAC,GAAG,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;QAEjC,IAAI,CAAC,GAAG,IAAI;YAAE,IAAI,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI;YAAE,IAAI,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;YAAE,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI;YAAE,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC;QAEzC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;QAEzD,UAAU,IAAI,CAAC,CAAC;QAChB,UAAU,IAAI,UAAU,CAAC;KAC1B;IAED,OAAO;QACL,QAAQ,UAAA;QACR,KAAK,OAAA;QACL,MAAM,EAAE;YACN,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,IAAI;YACP,KAAK,EAAE,IAAI,GAAG,IAAI;YAClB,MAAM,EAAE,IAAI,GAAG,IAAI;SACpB;KACF,CAAC;AACJ,CAAC,CAAC;AAeW,QAAA,oBAAoB,GAAG,UAClC,IAAY,EACZ,EAAkE;QAAhE,SAAS,eAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAA,EAAE,MAAM,YAAA;IAEnC,IAAM,IAAI,GAAG,kBAAU,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzC,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC5C,QAAQ,GAAG,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;KAClD;IAED,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtC,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACrD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;IAEjE,kBAAkB;IAClB,IAAM,CAAC,GAAG,CACN,SAAS,KAAK,yBAAa,CAAC,IAAI,CAAG,CAAC,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,SAAS,KAAK,yBAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;YAClF,CAAC,CAAC,SAAS,KAAK,yBAAa,CAAC,KAAK,CAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK;gBACtE,CAAC,CAAC,MAAM,CAAC,CAAC,CACX,CAAC;IAEF,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAEtD,OAAO;QACL,QAAQ,UAAA;QACR,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE;QAClD,MAAM,EAAE,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE;KAChC,CAAC;AACJ,CAAC,CAAC"}