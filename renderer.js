// --- Get Element References ---
const ebookFolderPathInput = document.getElementById("ebookFolderPath");
const outputPathInput = document.getElementById("outputPath");
const maxPagesInput = document.getElementById("maxPagesInput");
const startPageInput = document.getElementById("startPage");
const endPageInput = document.getElementById("endPage");
const singlePageInput = document.getElementById("singlePage");
const customWidthInput = document.getElementById("customWidth");
const customHeightInput = document.getElementById("customHeight");
const pcHeightInput = document.getElementById("pcHeight");
const phoneHeightInput = document.getElementById("phoneHeight");
const fontSizeInput = document.getElementById("fontSize");
const resetFontSizeButton = document.getElementById("resetFontSize");
const captureDelaySelect = document.getElementById("captureDelay");
const qualitySelect = document.getElementById("qualitySelect");

const browseFolderButton = document.getElementById("browseFolder");
const browseOutputButton = document.getElementById("browseOutput");
const serverButton = document.getElementById("serverButton");
const startButton = document.getElementById("startButton");
const stopButton = document.getElementById("stopButton");
const cleanupButton = document.getElementById("cleanupButton");
const statusDiv = document.getElementById("status");

const modeRadios = document.querySelectorAll('input[name="mode"]');
const pageRangeRadios = document.querySelectorAll('input[name="pageRange"]');
const customSizeRow = document.getElementById("customSizeRow");
const pcHeightRow = document.getElementById("pcHeightRow");
const phoneHeightRow = document.getElementById("phoneHeightRow");

// --- State Variables ---
let isServerRunning = false;
let isExtracting = false;
let isServerStarting = false;
let isCleaning = false;
let maxPages = 500; // Default max pages

// --- Helper Functions ---
function appendStatus(message) {
    const timestamp = new Date().toLocaleTimeString();
    statusDiv.textContent += `[${timestamp}] ${message}\n`;
    statusDiv.scrollTop = statusDiv.scrollHeight;
}

function toggleCustomSize() {
    const selectedMode = document.querySelector('input[name="mode"]:checked').value;

    // Show/hide appropriate height controls based on mode
    pcHeightRow.style.display = selectedMode === "pc" ? "flex" : "none";
    phoneHeightRow.style.display = selectedMode === "phone" ? "flex" : "none";
    customSizeRow.style.display = selectedMode === "custom" ? "flex" : "none";
}

function togglePageRangeInputs() {
    const rangeType = document.querySelector('input[name="pageRange"]:checked').value;

    maxPagesInput.disabled = rangeType !== "all";
    startPageInput.disabled = rangeType !== "specific";
    endPageInput.disabled = rangeType !== "specific";
    singlePageInput.disabled = rangeType !== "single";
}

function getPageConfig() {
    const rangeType = document.querySelector('input[name="pageRange"]:checked').value;

    switch (rangeType) {
        case "all":
            return {
                type: "all",
                maxPages: parseInt(maxPagesInput.value, 10) || 500
            };
        case "specific":
            return {
                type: "range",
                startPage: parseInt(startPageInput.value, 10) || 1,
                endPage: parseInt(endPageInput.value, 10) || 10
            };
        case "single":
            return {
                type: "single",
                pageNumber: parseInt(singlePageInput.value, 10) || 1
            };
        default:
            return { type: "all", maxPages: 500 };
    }
}

// --- Enable/Disable Controls ---
function setControlsEnabled() {
    const operationInProgress = isExtracting || isServerStarting || isCleaning;
    const folderSelected = !!ebookFolderPathInput.value;
    const outputSelected = !!outputPathInput.value;

    if (operationInProgress) {
        browseFolderButton.disabled = true;
        browseOutputButton.disabled = true;
        serverButton.disabled = true;
        startButton.disabled = true;
        cleanupButton.disabled = true;
        maxPagesInput.disabled = true;
        startPageInput.disabled = true;
        endPageInput.disabled = true;
        singlePageInput.disabled = true;
        customWidthInput.disabled = true;
        customHeightInput.disabled = true;
        pcHeightInput.disabled = true;
        phoneHeightInput.disabled = true;
        fontSizeInput.disabled = true;
        resetFontSizeButton.disabled = true;
        captureDelaySelect.disabled = true;
        qualitySelect.disabled = true;
        modeRadios.forEach((radio) => (radio.disabled = true));
        pageRangeRadios.forEach((radio) => (radio.disabled = true));
        stopButton.disabled = !isExtracting;
        return;
    }

    browseFolderButton.disabled = false;
    browseOutputButton.disabled = false;
    cleanupButton.disabled = false;
    pcHeightInput.disabled = false;
    phoneHeightInput.disabled = false;
    fontSizeInput.disabled = false;
    resetFontSizeButton.disabled = false;
    captureDelaySelect.disabled = false;
    qualitySelect.disabled = false;
    modeRadios.forEach((radio) => (radio.disabled = false));
    pageRangeRadios.forEach((radio) => (radio.disabled = false));

    serverButton.disabled = !folderSelected || isServerRunning;
    startButton.disabled = !folderSelected || !outputSelected || !isServerRunning;
    stopButton.disabled = true;

    togglePageRangeInputs();
    toggleCustomSize();
}


// --- Event Listeners for Buttons ---

// Browse Folder Button
browseFolderButton.addEventListener("click", async () => {
    appendStatus("Opening dialog to select E-book folder...");
    const folderPath = await window.electronAPI.selectFolder();
    if (folderPath) {
        ebookFolderPathInput.value = folderPath;
        appendStatus(`Selected E-book Folder: ${folderPath}`);
        isServerRunning = false;
        setControlsEnabled();
    } else {
        appendStatus("Folder selection cancelled.");
    }
});

// Browse Output Button
browseOutputButton.addEventListener("click", async () => {
    appendStatus("Opening dialog to select output PDF location...");
    const filePath = await window.electronAPI.selectOutput();
    if (filePath) {
        outputPathInput.value = filePath;
        appendStatus(`Selected Output PDF: ${filePath}`);
        setControlsEnabled();
    } else {
        appendStatus("Output location selection cancelled.");
    }
});

// Server Button
serverButton.addEventListener("click", () => {
    const folderPath = ebookFolderPathInput.value;
    if (!folderPath) {
        appendStatus("ERROR: Please select the E-book folder first.");
        return;
    }
    appendStatus("\n--- Attempting to Start Local Server ---");
    isServerStarting = true;
    isExtracting = false;
    isCleaning = false;
    setControlsEnabled();
    isServerRunning = false;
    window.electronAPI.startServer(folderPath);
});


// Start Button
startButton.addEventListener("click", () => {
    const ebookFolderPath = ebookFolderPathInput.value;
    const outputPath = outputPathInput.value;

    if (!ebookFolderPath || !outputPath) {
        appendStatus("ERROR: Cannot start - E-book Folder and Output PDF path must be selected.");
        return;
    }

    if (!isServerRunning) {
        appendStatus("ERROR: Cannot start - Local server is not running or not ready. Click 'Start Server' first.");
        return;
    }

    const pageConfig = getPageConfig();
    const mode = document.querySelector('input[name="mode"]:checked').value;

    // Validate page configuration
    if (pageConfig.type === "all" && (isNaN(pageConfig.maxPages) || pageConfig.maxPages < 1)) {
        appendStatus("ERROR: Please enter a valid number (1 or more) for Max Pages.");
        return;
    }

    if (pageConfig.type === "range") {
        if (isNaN(pageConfig.startPage) || isNaN(pageConfig.endPage) ||
            pageConfig.startPage < 1 || pageConfig.endPage < pageConfig.startPage) {
            appendStatus("ERROR: Please enter valid start and end page numbers (start <= end).");
            return;
        }
    }

    if (pageConfig.type === "single" && (isNaN(pageConfig.pageNumber) || pageConfig.pageNumber < 1)) {
        appendStatus("ERROR: Please enter a valid page number (1 or more).");
        return;
    }

    // Validate sizes based on mode
    let viewportWidth, viewportHeight;

    if (mode === "pc") {
        viewportWidth = 1024;
        viewportHeight = parseInt(pcHeightInput.value, 10);
        if (isNaN(viewportHeight) || viewportHeight < 1000) {
            appendStatus("ERROR: PC height must be at least 1000px.");
            return;
        }
    } else if (mode === "phone") {
        viewportWidth = 375;
        viewportHeight = parseInt(phoneHeightInput.value, 10);
        if (isNaN(viewportHeight) || viewportHeight < 1000) {
            appendStatus("ERROR: Phone height must be at least 1000px.");
            return;
        }
    } else if (mode === "custom") {
        viewportWidth = parseInt(customWidthInput.value, 10);
        viewportHeight = parseInt(customHeightInput.value, 10);
        if (isNaN(viewportWidth) || viewportWidth < 200 ||
            isNaN(viewportHeight) || viewportHeight < 1000) {
            appendStatus("ERROR: Custom size must be at least 200px wide and 1000px tall.");
            return;
        }
    }

    const extractionOptions = {
        url: "http://localhost:8000",
        outputFile: outputPath,
        mode: mode,
        viewportWidth: viewportWidth,
        viewportHeight: viewportHeight,
        fontSize: parseInt(fontSizeInput.value, 10) || 16,
        quality: qualitySelect.value,
        pageConfig: pageConfig
    };

    appendStatus("\n--- Starting Extraction Process ---");
    isExtracting = true;
    isServerStarting = false;
    isCleaning = false;
    setControlsEnabled();
    window.electronAPI.startExtraction(extractionOptions);
});

stopButton.addEventListener("click", () => {
    if (!isExtracting) return;
    appendStatus("--- Sending Stop Signal ---");
    window.electronAPI.stopExtraction();
});


cleanupButton.addEventListener("click", () => { /* ... keep existing code ... */
    appendStatus("\n--- Starting Temporary File Cleanup ---");
    isCleaning = true; isExtracting = false; isServerStarting = false;
    setControlsEnabled(); // Disable controls
    window.electronAPI.cleanupTempFiles();
});

// --- IPC Listeners for Updates from Main ---

window.electronAPI.onServerStatus((message) => { /* ... keep existing code ... */
    appendStatus(`${message}`);
    let reEnableControls = false;
    if (message.startsWith("SUCCESS: Local server is running")) { isServerRunning = true; isServerStarting = false; reEnableControls = true; }
    else if (message.startsWith("ERROR:") || message.includes("Failed to start") || message.includes("stopped unexpectedly")) { isServerRunning = false; isServerStarting = false; reEnableControls = true; }
    else if (message.startsWith("Stopping active local web server")) { isServerRunning = false; }
    else if (message.startsWith("Server assumed running")) { isServerRunning = true; isServerStarting = false; reEnableControls = true; }
    if (reEnableControls && !isExtracting && !isCleaning) { setControlsEnabled(); } // Check other flags too
});


window.electronAPI.onExtractionStatus((message) => { /* ... keep existing code ... */
  appendStatus(`${message}`);
});

window.electronAPI.onExtractionFinished((result) => { /* ... keep existing code ... */
  appendStatus(`--- Extraction ${result.success ? "Complete" : "Failed"} ---`);
  if (result.error) { appendStatus(`Error details: ${result.error}`); }
  isExtracting = false;
  setControlsEnabled(); // Re-evaluate controls
});

window.electronAPI.onCleanupStatus((message) => { /* ... keep existing code ... */
    appendStatus(`${message}`);
    if (message.startsWith("Cleanup finished:")) {
        isCleaning = false;
        setControlsEnabled(); // Re-evaluate controls
    }
});

// --- Event Listeners for UI Controls ---
modeRadios.forEach(radio => {
    radio.addEventListener('change', toggleCustomSize);
});

pageRangeRadios.forEach(radio => {
    radio.addEventListener('change', togglePageRangeInputs);
});

// --- Input Validation ---
customWidthInput.addEventListener('input', () => {
    if (customWidthInput.value && parseInt(customWidthInput.value) < 200) {
        customWidthInput.value = 200;
    }
});

customHeightInput.addEventListener('input', () => {
    if (customHeightInput.value && parseInt(customHeightInput.value) < 1000) {
        customHeightInput.value = 1000;
    }
});

maxPagesInput.addEventListener('input', () => {
    if (maxPagesInput.value && parseInt(maxPagesInput.value) < 1) {
        maxPagesInput.value = 1;
    }
});

startPageInput.addEventListener('input', () => {
    if (startPageInput.value && parseInt(startPageInput.value) < 1) {
        startPageInput.value = 1;
    }
});

endPageInput.addEventListener('input', () => {
    if (endPageInput.value) {
        const startVal = parseInt(startPageInput.value) || 1;
        const endVal = parseInt(endPageInput.value);
        if (endVal < startVal) {
            endPageInput.value = startVal;
        }
    }
});

singlePageInput.addEventListener('input', () => {
    if (singlePageInput.value && parseInt(singlePageInput.value) < 1) {
        singlePageInput.value = 1;
    }
});

pcHeightInput.addEventListener('input', () => {
    if (pcHeightInput.value && parseInt(pcHeightInput.value) < 1000) {
        pcHeightInput.value = 1000;
    }
});

phoneHeightInput.addEventListener('input', () => {
    if (phoneHeightInput.value && parseInt(phoneHeightInput.value) < 1000) {
        phoneHeightInput.value = 1000;
    }
});

fontSizeInput.addEventListener('input', () => {
    const value = parseInt(fontSizeInput.value);
    if (fontSizeInput.value && (value < 8 || value > 72)) {
        fontSizeInput.value = Math.max(8, Math.min(72, value));
    }
});

resetFontSizeButton.addEventListener('click', () => {
    fontSizeInput.value = 16;
    appendStatus("Font size reset to 16px");
});

// --- Initialize UI State ---
document.addEventListener('DOMContentLoaded', () => {
    toggleCustomSize();
    togglePageRangeInputs();
    setControlsEnabled();
    appendStatus("Application loaded. Ready to begin extraction process.");
});

// --- Initial State ---
setControlsEnabled();
appendStatus("Ready. Please select the extracted E-book folder.");

// --- Cleanup ---
window.addEventListener("beforeunload", () => {
  window.electronAPI.removeAllListeners("server-status");
  window.electronAPI.removeAllListeners("extraction-status");
  window.electronAPI.removeAllListeners("extraction-finished");
  window.electronAPI.removeAllListeners("cleanup-status");
});
