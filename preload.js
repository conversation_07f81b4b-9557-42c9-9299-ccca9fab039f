const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

contextBridge.exposeInMainWorld("electronAPI", {
  // Renderer -> Main (Invoke/Handle pattern)
  selectFolder: () => ipcRenderer.invoke("dialog:openDirectory"),
  selectOutput: () => ipcRenderer.invoke("dialog:savePdf"),

  // Renderer -> Main (Send pattern)
  startServer: (folderPath) => ipcRenderer.send('start-server', folderPath),
  startExtraction: (options) => ipcRenderer.send("start-extraction", options),
  stopExtraction: () => ipcRenderer.send("stop-extraction"),
  cleanupTempFiles: () => ipcRenderer.send("cleanup-temp-files"),

  // Main -> Renderer (Receive messages from Main)
  onServerStatus: (callback) =>
    ipcRenderer.on('server-status', (_event, value) => callback(value)),
  onExtractionStatus: (callback) =>
    ipcRenderer.on("extraction-status", (_event, value) => callback(value)),
  onExtractionFinished: (callback) =>
    ipcRenderer.on("extraction-finished", (_event, value) =>
      callback(value),
    ),
  onCleanupStatus: (callback) =>
    ipcRenderer.on("cleanup-status", (_event, value) => callback(value)),

  // Cleanup
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
});
