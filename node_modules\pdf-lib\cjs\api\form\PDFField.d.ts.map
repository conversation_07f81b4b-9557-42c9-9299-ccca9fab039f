{"version": 3, "file": "PDFField.d.ts", "sourceRoot": "", "sources": ["../../../src/api/form/PDFField.ts"], "names": [], "mappings": "AAAA,OAAO,WAAW,uBAA4B;AAC9C,OAAO,OAAO,mBAAwB;AACtC,OAAO,EAAE,iBAAiB,EAAE,sBAAiC;AAC7D,OAAO,EAAE,KAAK,EAAsC,kBAAuB;AAC3E,OAAO,EACL,QAAQ,EAMT,qBAA0B;AAE3B,OAAO,EACL,MAAM,EACN,mBAAmB,EACnB,WAAW,EACX,OAAO,EACP,OAAO,EAGP,eAAe,EAEhB,mBAAiB;AAElB,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAC1C,OAAO,QAAQ,MAAM,aAAa,CAAC;AAGnC,MAAM,WAAW,sBAAsB;IACrC,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,KAAK,CAAC;IAClB,eAAe,CAAC,EAAE,KAAK,CAAC;IACxB,WAAW,CAAC,EAAE,KAAK,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,QAAQ,CAAC;IAClB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,eAAO,MAAM,4BAA4B,wDAkBxC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,OAAO,OAAO,QAAQ;IAC3B,2DAA2D;IAC3D,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC;IAEpC,uEAAuE;IACvE,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC;IAErB,gDAAgD;IAChD,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC;IAE1B,SAAS,aACP,SAAS,EAAE,eAAe,EAC1B,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,WAAW;IAWlB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,OAAO,IAAI,MAAM;IAIjB;;;;;;;;;;OAUG;IACH,UAAU,IAAI,OAAO;IAIrB;;;;;;;;;;OAUG;IACH,cAAc;IAId;;;;;;;OAOG;IACH,eAAe;IAIf;;;;;;;;;OASG;IACH,UAAU,IAAI,OAAO;IAIrB;;;;;;;OAOG;IACH,cAAc;IAId;;;;;;;OAOG;IACH,eAAe;IAIf;;;;;;;;;;OAUG;IACH,UAAU,IAAI,OAAO;IAIrB;;;;;;;OAOG;IACH,eAAe;IAIf;;;;;;;OAOG;IACH,gBAAgB;IAIhB,cAAc;IACd,sBAAsB,IAAI,OAAO;IAOjC,cAAc;IACd,wBAAwB,CAAC,KAAK,EAAE,OAAO;IAOvC,SAAS,CAAC,WAAW;IAIrB,SAAS,CAAC,WAAW;IAIrB,SAAS,CAAC,OAAO,IAAI,OAAO;IAI5B,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE;QAC9B,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,KAAK,CAAC;QAClB,eAAe,CAAC,EAAE,KAAK,CAAC;QACxB,WAAW,CAAC,EAAE,KAAK,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,MAAM,EAAE,QAAQ,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,GAAG,mBAAmB;IAsDvB,SAAS,CAAC,8BAA8B,CACtC,MAAM,EAAE,mBAAmB,EAC3B,IAAI,EAAE,OAAO,EACb,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,iBAAiB,CAAC,WAAW,EAAE,CAAC;IAS9D,SAAS,CAAC,2BAA2B,CACnC,MAAM,EAAE,mBAAmB,EAC3B,OAAO,EAAE,OAAO,EAChB,EACE,MAAM,EACN,QAAQ,EACR,IAAI,GACL,EAAE,iBAAiB,CAAC;QAAE,EAAE,EAAE,WAAW,EAAE,CAAC;QAAC,GAAG,EAAE,WAAW,EAAE,CAAA;KAAE,CAAC;IAUjE,SAAS,CAAC,uBAAuB,CAC/B,MAAM,EAAE,mBAAmB,EAC3B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC;IAgCjE,OAAO,CAAC,sBAAsB;IA0B9B;;;;;;;;OAQG;IACH,SAAS,CAAC,2BAA2B,CACnC,MAAM,EAAE,mBAAmB,EAC3B,KAAK,EAAE,QAAQ,EACf,SAAS,EAAE,cAAc,GACxB,MAAM;IAyDT,OAAO,CAAC,oBAAoB;CAgB7B"}