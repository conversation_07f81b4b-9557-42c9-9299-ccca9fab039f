{"version": 3, "file": "PDFForm.js", "sourceRoot": "", "sources": ["../../../src/api/form/PDFForm.ts"], "names": [], "mappings": ";;;AAAA,uEAA8C;AAG9C,kEAA+C;AAC/C,sEAAmD;AACnD,sEAAmD;AACnD,0EAAuD;AACvD,0EAAuD;AACvD,wEAAqD;AACrD,wEAAqD;AACrD,oCAKwB;AACxB,+DAAsC;AACtC,kDAAsD;AACtD,4CAAmD;AACnD,0CAK2B;AAC3B,mCAiBkB;AAClB,qCAA+D;AAM/D;;;;;;;;;;GAUG;AACH;IAuBE,iBAAoB,QAAqB,EAAE,GAAgB;QAA3D,iBASC;QAyrBO,qBAAgB,GAAG;YACzB,OAAA,KAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,6BAAa,CAAC,SAAS,CAAC;QAAnD,CAAmD,CAAC;QAlsBpD,gBAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,kBAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QAC/D,gBAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,qBAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,wBAAM,GAAN;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,cAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;OAUG;IACH,2BAAS,GAAT;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,cAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,2BAAS,GAAT;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;QAE/C,IAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACpD,IAAA,KAAmB,SAAS,CAAC,GAAG,CAAC,EAAhC,SAAS,QAAA,EAAE,GAAG,QAAkB,CAAC;YACxC,IAAM,KAAK,GAAG,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1D,IAAI,KAAK;gBAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/B;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACH,+BAAa,GAAb,UAAc,IAAY;QACxB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI;gBAAE,OAAO,KAAK,CAAC;SAC5C;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;OASG;IACH,0BAAQ,GAAR,UAAS,IAAY;QACnB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,KAAK;YAAE,OAAO,KAAK,CAAC;QACxB,MAAM,IAAI,yBAAgB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;OAUG;IACH,2BAAS,GAAT,UAAU,IAAY;QACpB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,mBAAS;YAAE,OAAO,KAAK,CAAC;QAC7C,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,mBAAS,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,6BAAW,GAAX,UAAY,IAAY;QACtB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,qBAAW;YAAE,OAAO,KAAK,CAAC;QAC/C,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,qBAAW,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,6BAAW,GAAX,UAAY,IAAY;QACtB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,qBAAW;YAAE,OAAO,KAAK,CAAC;QAC/C,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,qBAAW,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,+BAAa,GAAb,UAAc,IAAY;QACxB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,uBAAa;YAAE,OAAO,KAAK,CAAC;QACjD,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,uBAAa,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,+BAAa,GAAb,UAAc,IAAY;QACxB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,uBAAa;YAAE,OAAO,KAAK,CAAC;QACjD,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,uBAAa,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;;;;;OAWG;IACH,8BAAY,GAAZ,UAAa,IAAY;QACvB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,sBAAY;YAAE,OAAO,KAAK,CAAC;QAChD,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,sBAAY,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,8BAAY,GAAZ,UAAa,IAAY;QACvB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,YAAY,sBAAY;YAAE,OAAO,KAAK,CAAC;QAChD,MAAM,IAAI,iCAAwB,CAAC,IAAI,EAAE,sBAAY,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,8BAAY,GAAZ,UAAa,IAAY;QACvB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,MAAM,GAAG,wBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1C,gBAAgB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEnE,OAAO,mBAAS,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,gCAAc,GAAd,UAAe,IAAY;QACzB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,QAAQ,GAAG,sBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1D,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE5C,gBAAgB,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEvE,OAAO,qBAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,gCAAc,GAAd,UAAe,IAAY;QACzB,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,QAAQ,GAAG,sBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1D,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE5C,gBAAgB,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEvE,OAAO,qBAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,kCAAgB,GAAhB,UAAiB,IAAY;QAC3B,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,OAAO,GAAG,qBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxD,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE3C,gBAAgB,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAErE,OAAO,uBAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,kCAAgB,GAAhB,UAAiB,IAAY;QAC3B,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,WAAW,GAAG,yBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChE,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE/C,gBAAgB,CACd,MAAM,EACN,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,EAC9B,SAAS,CAAC,QAAQ,CACnB,CAAC;QAEF,OAAO,uBAAa,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,iCAAe,GAAf,UAAgB,IAAY;QAC1B,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAM,IAAI,GAAG,kBAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAExC,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE/D,OAAO,sBAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,yBAAO,GAAP,UAAQ,OAA0D;QAA1D,wBAAA,EAAA,YAA4B,sBAAsB,EAAE,IAAI,EAAE;QAChE,IAAI,OAAO,CAAC,sBAAsB,EAAE;YAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;SAC/B;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAC7D,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBAChE,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACzC,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAE9D,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAEjE,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxC,IAAM,SAAS,GAAG;oBAChB,6BAAiB,EAAE;oBACnB,qBAAS,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;mBAChC,0BAAa,uCAAM,SAAS,KAAE,QAAQ,EAAE,CAAC,IAAG;oBAC/C,sBAAU,CAAC,UAAU,CAAC;oBACtB,4BAAgB,EAAE;mBAClB,MAAM,CAAC,OAAO,CAAkB,CAAC;gBAEnC,IAAI,CAAC,aAAa,OAAlB,IAAI,EAAkB,SAAS,EAAE;aAClC;YAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,6BAAW,GAAX,UAAY,KAAe;QACzB,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC7C,IAAM,KAAK,GAAiB,IAAI,GAAG,EAAE,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAClD,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE9D,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACzC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEhB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SAClC;QAED,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC;QAC3D,IAAM,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QACnC,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,SAAS,EAAE,UAAU,EAAE,EAAE;YAC7D,IAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxC,IAAI,KAAK,YAAY,aAAM,EAAE;gBAC3B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAChC;SACF;QACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,wCAAsB,GAAtB,UAAuB,IAAc;QACnC,yBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,iBAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QAExD,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QAErC,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,sBAAsB,EAAE,EAAE;gBAClC,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;aACtC;SACF;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,kCAAgB,GAAhB,UAAiB,QAAgB;QAC/B,yBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,aAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;;OASG;IACH,kCAAgB,GAAhB,UAAiB,QAAgB;QAC/B,yBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,aAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;OASG;IACH,8BAAY,GAAZ,UAAa,QAAgB;QAC3B,yBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,aAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,gCAAc,GAAd;QACE,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;IACxC,CAAC;IAEO,gCAAc,GAAtB,UAAuB,MAA2B;QAChD,IAAM,OAAO,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;QAC3B,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,KAAK,OAAO,EAAjB,CAAiB,CAAC,CAAC;QAC9D,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7D,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;aACxD;YAED,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAEpD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,IAAI,KAAK,CAAC,oCAAkC,SAAW,CAAC,CAAC;aAChE;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,yCAAuB,GAA/B,UACE,KAAe,EACf,MAA2B;;QAE3B,IAAI,SAAS,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IACE,SAAS,YAAY,cAAO;YAC5B,CAAC,KAAK,YAAY,qBAAW,IAAI,KAAK,YAAY,uBAAa,CAAC,EAChE;YACA,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAM,GAAG,SAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAI,SAAS,CAAC,GAAG,CAAC,cAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAErE,IAAI,GAAG,YAAY,aAAM,EAAE;gBACzB,SAAS,GAAG,GAAG,CAAC;aACjB;SACF;QAED,IAAI,CAAC,CAAC,SAAS,YAAY,aAAM,CAAC,EAAE;YAClC,IAAM,MAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,2CAAyC,MAAM,CAAC,CAAC;SAClE;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,0CAAwB,GAAhC,UAAiC,YAAsB;QACrD,IAAI,WAAW,GAAiD;YAC9D,IAAI,CAAC,QAAQ;SACd,CAAC;QACF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAC7D,IAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAI,kCAAyB,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAA,QAAM,GAAe,WAAW,GAA1B,EAAE,SAAS,GAAI,WAAW,GAAf,CAAgB;YACxC,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAM,CAAC,CAAC;YAEnD,IAAI,GAAG,EAAE;gBACP,WAAW,GAAG,GAAG,CAAC;aACnB;iBAAM;gBACL,IAAM,IAAI,GAAG,yBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC1B,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrD,QAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACzB,WAAW,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAC/B;SACF;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,iCAAe,GAAvB,UACE,WAAmB,EACnB,MAAwC;QAExC,IAAM,MAAM,GACV,MAAM,YAAY,kBAAW;YAC3B,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAC3B,CAAC,CAAC,0BAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,IAAA,KAAe,MAAM,CAAC,GAAG,CAAC,EAAzB,KAAK,QAAA,EAAE,GAAG,QAAe,CAAC;YACjC,IAAI,KAAK,CAAC,cAAc,EAAE,KAAK,WAAW,EAAE;gBAC1C,IAAI,KAAK,YAAY,yBAAkB;oBAAE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC7D,MAAM,IAAI,gCAAuB,CAAC,WAAW,CAAC,CAAC;aAChD;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAttBD;;;;;;;;;OASG;IACI,UAAE,GAAG,UAAC,QAAqB,EAAE,GAAgB;QAClD,OAAA,IAAI,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;IAA1B,CAA0B,CAAC;IA+sB/B,cAAC;CAAA,AA3tBD,IA2tBC;kBA3tBoB,OAAO;AA6tB5B,IAAM,iBAAiB,GAAG,UACxB,KAAmB,EACnB,GAAW,EACX,GAAgB;IAEhB,IAAI,KAAK,YAAY,wBAAiB;QAAE,OAAO,mBAAS,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7E,IAAI,KAAK,YAAY,sBAAe;QAAE,OAAO,qBAAW,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7E,IAAI,KAAK,YAAY,sBAAe;QAAE,OAAO,qBAAW,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7E,IAAI,KAAK,YAAY,qBAAc;QAAE,OAAO,uBAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9E,IAAI,KAAK,YAAY,kBAAW;QAAE,OAAO,sBAAY,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,KAAK,YAAY,yBAAkB,EAAE;QACvC,OAAO,uBAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;KAC1C;IACD,IAAI,KAAK,YAAY,uBAAgB,EAAE;QACrC,OAAO,sBAAY,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;KACzC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,IAAM,cAAc,GAAG,UAAC,kBAA0B;IAChD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,IAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE;YACrB,MAAM,IAAI,KAAK,CACb,+EAA4E,kBAAkB,OAAG,CAClG,CAAC;SACH;KACF;IAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAEvE,OAAO;QACL,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7C,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KAClC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAM,gBAAgB,GAAG,UACvB,EAAiE,EACjE,EAAyC,EACzC,WAAmB;QAFlB,MAAM,QAAA,EAAE,SAAS,QAAA;QACjB,KAAK,QAAA,EAAE,QAAQ,QAAA;IAGhB,IAAM,OAAO,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAM,MAAM,GAAG,0BAAmB,CAChC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAClD,CAAC;IACF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACvD,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,KAAK,WAAW,EAAE;YACnD,MAAM,IAAI,gCAAuB,CAAC,WAAW,CAAC,CAAC;SAChD;KACF;IACD,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC7B,CAAC,CAAC"}