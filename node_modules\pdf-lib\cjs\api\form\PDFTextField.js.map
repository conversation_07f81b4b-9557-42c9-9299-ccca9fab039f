{"version": 3, "file": "PDFTextField.js", "sourceRoot": "", "sources": ["../../../src/api/form/PDFTextField.ts"], "names": [], "mappings": ";;;AACA,+DAAsC;AACtC,+DAAsC;AAEtC,6DAG+B;AAC/B,6CAIkC;AAClC,oCAAqC;AACrC,0CAA4C;AAC5C,oCAIwB;AACxB,gDAAyD;AACzD,+CAAuD;AAEvD,mCAOkB;AAClB,qCAMmB;AAEnB;;;;;;;;GAQG;AACH;IAA0C,wCAAQ;IAkBhD,sBAAoB,QAAqB,EAAE,GAAW,EAAE,GAAgB;QAAxE,YACE,kBAAM,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,SAK1B;QAHC,gBAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,kBAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QAE/D,KAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;;IAC5B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,8BAAO,GAAP;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YACpC,MAAM,IAAI,+BAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAClD;QACD,OAAO,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,UAAU,GAAG;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,8BAAO,GAAP,UAAQ,IAAwB;QAC9B,yBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5C,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;YAC9D,MAAM,IAAI,+BAAsB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC1E;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;SACtD;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;SAC9B;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,mCAAY,GAAZ;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAE9C,kBAAkB;QAClB,OAAO,CACH,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAa,CAAC,IAAI;YACrC,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAa,CAAC,MAAM;gBACvC,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAa,CAAC,KAAK;oBACtC,CAAC,CAAC,yBAAa,CAAC,IAAI,CACrB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,mCAAY,GAAZ,UAAa,SAAwB;QACnC,qBAAa,CAAC,SAAS,EAAE,WAAW,EAAE,yBAAa,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,mCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,mCAAY,GAAZ,UAAa,SAAkB;QAC7B,8BAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAE3E,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;SAClC;aAAM;YACL,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;gBACnC,MAAM,IAAI,8BAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;aACzE;YACD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SACxC;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,sCAAe,GAAf;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACH,+BAAQ,GAAR,UAAS,KAAe;QACtB,IAAM,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAE3C,kBAAkB;QAClB,IAAM,SAAS,GACX,cAAc,KAAK,yBAAa,CAAC,MAAM,CAAC,CAAC,CAAC,0BAAc,CAAC,MAAM;YACjE,CAAC,CAAC,cAAc,KAAK,yBAAa,CAAC,KAAK,CAAC,CAAC,CAAC,0BAAc,CAAC,KAAK;gBAC/D,CAAC,CAAC,0BAAc,CAAC,IAAI,CAAC;QAExB,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACxD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAM,SAAS,GAAG,IAAI,CAAC,2BAA2B,CAChD,MAAM,EACN,KAAK,EACL,SAAS,CACV,CAAC;YACF,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,kCAAW,GAAX,UAAY,QAAgB;QAC1B,sBAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;OAWG;IACH,kCAAW,GAAX;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;;;OASG;IACH,sCAAe,GAAf;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;OASG;IACH,uCAAgB,GAAhB;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;OAUG;IACH,iCAAU,GAAV;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,qCAAc,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACH,sCAAe,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;OASG;IACH,qCAAc,GAAd;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;OAQG;IACH,0CAAmB,GAAnB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;OAOG;IACH,2CAAoB,GAApB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;;OASG;IACH,qCAAc,GAAd;QACE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,eAAe,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACH,0CAAmB,GAAnB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;OAOG;IACH,2CAAoB,GAApB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;OAUG;IACH,mCAAY,GAAZ;QACE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;;OASG;IACH,sCAAe,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,uCAAgB,GAAhB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,+BAAQ,GAAR;QACE,OAAO,CACL,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,IAAI,CAAC;YAC1C,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,YAAY,EAAE,KAAK,SAAS,CAClC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,oCAAa,GAAb;QACE,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,SAAS,EAAE;YACrC,IAAM,GAAG,GAAG,4DAA4D,CAAC;YACzE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnB;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;OAWG;IACH,qCAAc,GAAd;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;;OASG;IACH,sCAAe,GAAf;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAa,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,2CAAoB,GAApB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACH,4CAAqB,GAArB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,gCAAS,GAAT,UAAU,IAAa,EAAE,OAAgC;;QACvD,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,iBAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/C,uCAA4B,CAAC,OAAO,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAE,CAAC;QAE3B,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,CAAC;YAAE,OAAO,CAAC,SAAS,GAAG,YAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,CAAC,iBAAiB,IAAI,OAAO,CAAC;YAAE,OAAO,CAAC,eAAe,GAAG,YAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,CAAC,aAAa,IAAI,OAAO,CAAC;YAAE,OAAO,CAAC,WAAW,GAAG,YAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,CAAC,aAAa,IAAI,OAAO,CAAC;YAAE,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;QAEzD,sCAAsC;QACtC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YAC/B,CAAC,QAAE,OAAO,CAAC,CAAC,mCAAI,CAAC;YACjB,CAAC,QAAE,OAAO,CAAC,CAAC,mCAAI,CAAC;YACjB,KAAK,QAAE,OAAO,CAAC,KAAK,mCAAI,GAAG;YAC3B,MAAM,QAAE,OAAO,CAAC,MAAM,mCAAI,EAAE;YAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,QAAE,OAAO,CAAC,WAAW,mCAAI,CAAC;YACrC,MAAM,QAAE,OAAO,CAAC,MAAM,mCAAI,mBAAO,CAAC,CAAC,CAAC;YACpC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,IAAI,EAAE,IAAI,CAAC,GAAG;SACf,CAAC,CAAC;QACH,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEzD,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAEpC,oCAAoC;QACpC,IAAM,IAAI,SAAG,OAAO,CAAC,IAAI,mCAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,cAAc,EAAE,CAAC;QACjE,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE1C,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;OAQG;IACH,6CAAsB,GAAtB;;QACE,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,IAAI,CAAC;QAEhC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACxD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAM,cAAc,GAClB,OAAA,MAAM,CAAC,cAAc,EAAE,0CAAE,MAAM,aAAY,gBAAS,CAAC;YACvD,IAAI,CAAC,cAAc;gBAAE,OAAO,IAAI,CAAC;SAClC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG;IACH,+CAAwB,GAAxB,UAAyB,IAAa;QACpC,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,iBAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,wCAAiB,GAAjB,UACE,IAAa,EACb,QAA8C;QAE9C,gBAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,iBAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/C,yBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEpD,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACxD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,6CAAsB,GAA9B,UACE,MAA2B,EAC3B,IAAa,EACb,QAA8C;QAE9C,IAAM,UAAU,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,gDAAkC,CAAC;QAClE,IAAM,WAAW,GAAG,iCAAmB,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACjE,CAAC;IAvwBD;;;;;;;;;;OAUG;IACI,eAAE,GAAG,UAAC,QAAqB,EAAE,GAAW,EAAE,GAAgB;QAC/D,OAAA,IAAI,YAAY,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC;IAApC,CAAoC,CAAC;IA4vBzC,mBAAC;CAAA,AAzwBD,CAA0C,kBAAQ,GAywBjD;kBAzwBoB,YAAY"}