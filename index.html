<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta
        http-equiv="Content-Security-Policy"
        content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'"
    />
    <title>E-book Extractor Pro - Enhanced</title>
    <style>
        /* --- Global Styles & Variables --- */
        :root {
            --primary-orange: #f97316;
            --primary-orange-dark: #ea580c;
            --secondary-orange: #fb923c;
            --text-dark: #1f2937;
            --text-medium: #4b5563;
            --text-light: #6b7280;
            --bg-main: #f9fafb;
            --bg-container: #ffffff;
            --border-color: #e5e7eb;
            --input-bg: #f3f4f6;
            --success-green: #10b981;
            --success-green-dark: #059669;
            --warning-yellow: #f59e0b;
            --warning-yellow-dark: #d97706;
            --danger-red: #ef4444;
            --danger-red-dark: #dc2626;
            --status-bg: #f8f9fa;
            --status-border: #e9ecef;
            --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o,
                Helvetica, Arial, sans-serif;
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
                0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
                0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 0.5rem;
            --transition-speed: 0.2s;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-sans);
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: 16px;
            line-height: 1.6;
            padding: 2rem;
        }

        /* --- Main Container --- */
        .container {
            background-color: var(--bg-container);
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            max-width: 900px;
            margin: auto;
            border: 1px solid var(--border-color);
        }

        h1 {
            text-align: center;
            margin-bottom: 1.5rem;
            color: var(--primary-orange);
            font-weight: 700;
            font-size: 1.75rem;
        }

        /* --- Instructions --- */
        .instructions {
            background-color: #fffbeb;
            border-left: 4px solid var(--warning-yellow);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            font-size: 0.95em;
            border-radius: 0.375rem;
            color: #78350f;
        }
        .instructions b {
            font-weight: 600;
            color: #92400e;
        }
        .instructions ol {
            margin-top: 0.5rem;
            padding-left: 1.25rem;
        }
        .instructions li {
            margin-bottom: 0.25rem;
        }

        /* --- Input Groups --- */
        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 1.25rem;
            gap: 0.75rem;
        }

        .input-group label {
            flex-basis: 140px;
            flex-shrink: 0;
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--text-medium);
            text-align: right;
            padding-right: 0.5rem;
        }

        .input-group input[type="text"],
        .input-group input[type="number"],
        .input-group select {
            flex-grow: 1;
            padding: 0.65rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--input-bg);
            font-size: 0.95rem;
            color: var(--text-medium);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .input-group input[type="text"]:read-only {
            cursor: default;
        }

        .input-group input[type="number"] {
            max-width: 100px;
            flex-grow: 0;
        }

        .input-group select {
            max-width: 150px;
            flex-grow: 0;
            cursor: pointer;
        }

        /* --- Advanced Configuration Section --- */
        .advanced-config {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .advanced-config h3 {
            margin-bottom: 1rem;
            font-size: 1.1rem;
            color: var(--text-medium);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .config-row {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 200px;
        }

        .config-item label {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--text-medium);
            white-space: nowrap;
        }

        .mode-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .mode-group input[type="radio"] {
            accent-color: var(--primary-orange);
            width: 1.1em;
            height: 1.1em;
            cursor: pointer;
        }

        .mode-group label {
            font-weight: normal;
            font-size: 0.95rem;
            color: var(--text-medium);
            cursor: pointer;
        }

        .page-range-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .page-range-inputs {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-range-inputs input {
            width: 80px;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            text-align: center;
        }

        .custom-size-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .size-inputs {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .size-inputs input {
            width: 80px;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            text-align: center;
        }

        .font-size-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .font-size-group input {
            width: 60px;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            text-align: center;
        }

        .reset-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
            min-width: auto !important;
            background-color: var(--text-light) !important;
            color: white !important;
        }

        .reset-btn:hover:not(:disabled) {
            background-color: var(--text-medium) !important;
        }

        /* --- Buttons --- */
        button {
            padding: 0.65rem 1.25rem;
            border: none;
            border-radius: 0.375rem;
            color: white;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all var(--transition-speed) ease-in-out;
            box-shadow: var(--shadow-md);
        }

        button:hover:not(:disabled) {
            box-shadow: var(--shadow-lg);
            transform: translateY(-1px);
        }

        button:active:not(:disabled) {
            transform: translateY(0px);
            box-shadow: var(--shadow-md);
        }

        button:disabled {
            background-color: #d1d5db !important;
            color: #9ca3af !important;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .input-group button {
            flex-shrink: 0;
            background-color: var(--text-light);
            min-width: 90px;
        }

        .input-group button:hover:not(:disabled) {
            background-color: var(--text-medium);
        }

        #serverButton {
            background-color: var(--success-green);
        }

        #serverButton:hover:not(:disabled) {
            background-color: var(--success-green-dark);
        }

        #startButton {
            background-color: var(--primary-orange);
        }

        #startButton:hover:not(:disabled) {
            background-color: var(--primary-orange-dark);
        }

        #stopButton {
            background-color: var(--danger-red);
        }

        #stopButton:hover:not(:disabled) {
            background-color: var(--danger-red-dark);
        }

        #cleanupButton {
            background-color: var(--warning-yellow);
            color: var(--text-dark);
        }

        #cleanupButton:hover:not(:disabled) {
            background-color: var(--warning-yellow-dark);
        }

        /* --- Action Buttons --- */
        .action-buttons {
            margin-top: 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .action-buttons button {
            min-width: 160px;
        }

        /* --- Troubleshooting Section --- */
        .troubleshooting-section {
            margin-top: 2.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }

        .troubleshooting-section h3 {
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
            color: var(--text-medium);
            font-weight: 600;
            text-align: left;
        }

        .troubleshooting-section p {
            font-size: 0.9em;
            color: var(--text-light);
            margin-top: 0.75rem;
            max-width: 80%;
            margin-left: auto;
            margin-right: auto;
            text-align: center;
        }

        /* --- Status Log --- */
        #status-container {
            margin-top: 2.5rem;
        }

        #status-container h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-medium);
        }

        #status {
            border: 1px solid var(--status-border);
            background-color: var(--status-bg);
            padding: 1rem;
            height: 300px;
            overflow-y: scroll;
            white-space: pre-wrap;
            font-family: Consolas, "Courier New", monospace;
            font-size: 0.9em;
            border-radius: 0.375rem;
            line-height: 1.5;
            color: var(--text-dark);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06);
        }

        /* --- Utility Classes --- */
        .hidden {
            display: none !important;
        }

        .text-small {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        /* --- Responsive Design --- */
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
                margin: 1rem;
            }

            .config-row {
                flex-direction: column;
                gap: 1rem;
            }

            .config-item {
                min-width: auto;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-book Extractor Pro - Enhanced</h1>

        <div class="instructions">
            <b>Instructions:</b> (Requires Python 3 installed and in system PATH)
            <ol>
                <li>Extract the e-book `.exe` (treat it like a `.zip`) to a folder.</li>
                <li>Select the folder containing the extracted `index.html` file.</li>
                <li>Click "Start Server" and wait for the success message in the status log below.</li>
                <li>Configure your extraction settings (mode, pages, capture size).</li>
                <li>Select where to save the final PDF file.</li>
                <li>Click "Start Extraction". A separate browser window will open. You can click "Stop Capture" anytime to compile the pages captured so far.</li>
                <li><b>Troubleshooting:</b> If extraction fails, click "Clean Temp Files", then retry steps 2-6. The server stops automatically when you close this application.</li>
            </ol>
        </div>

        <!-- Input for E-book FOLDER -->
        <div class="input-group">
            <label for="ebookFolderPath">1. E-book Folder:</label>
            <input type="text" id="ebookFolderPath" readonly placeholder="Select folder containing index.html..." />
            <button id="browseFolder">Browse</button>
        </div>

        <!-- Advanced Configuration Section -->
        <div class="advanced-config">
            <h3>📋 Extraction Configuration</h3>

            <!-- Mode Selection Row -->
            <div class="config-row">
                <div class="config-item">
                    <label>Output Mode:</label>
                    <div class="mode-group">
                        <input type="radio" id="modePc" name="mode" value="pc" checked />
                        <label for="modePc">PC (1024px)</label>
                        <input type="radio" id="modePhone" name="mode" value="phone" />
                        <label for="modePhone">Phone (375px)</label>
                        <input type="radio" id="modeCustom" name="mode" value="custom" />
                        <label for="modeCustom">Custom Size</label>
                    </div>
                </div>
            </div>

            <!-- Custom Size Row -->
            <div class="config-row" id="customSizeRow">
                <div class="config-item">
                    <label>Custom Size:</label>
                    <div class="custom-size-group">
                        <div class="size-inputs">
                            <input type="number" id="customWidth" value="1024" min="200" max="3000" placeholder="Width" />
                            <span>×</span>
                            <input type="number" id="customHeight" value="10000" min="1000" max="50000" placeholder="Height" />
                            <span class="text-small">px</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Font Size Row -->
            <div class="config-row">
                <div class="config-item">
                    <label>Font Size:</label>
                    <div class="font-size-group">
                        <input type="number" id="fontSize" value="16" min="8" max="72" step="1" title="Adjust font size for better readability (8-72px)" />
                        <span class="text-small">px</span>
                        <button type="button" id="resetFontSize" class="reset-btn" title="Reset to default 16px">Reset</button>
                    </div>
                </div>
                <div class="config-item">
                    <span class="text-small">💡 Useful for mobile sizes (375px) to prevent oversized text</span>
                </div>
            </div>

            <!-- Page Range Selection Row -->
            <div class="config-row">
                <div class="config-item">
                    <label>Page Range:</label>
                    <div class="page-range-group">
                        <input type="radio" id="rangeAll" name="pageRange" value="all" checked />
                        <label for="rangeAll">All Pages (Max:</label>
                        <input type="number" id="maxPagesInput" value="500" min="1" step="1" />
                        <label for="rangeAll">)</label>
                    </div>
                </div>
            </div>

            <div class="config-row">
                <div class="config-item">
                    <div class="page-range-group">
                        <input type="radio" id="rangeSpecific" name="pageRange" value="specific" />
                        <label for="rangeSpecific">Specific Range:</label>
                        <div class="page-range-inputs">
                            <input type="number" id="startPage" value="1" min="1" step="1" placeholder="Start" />
                            <span>to</span>
                            <input type="number" id="endPage" value="10" min="1" step="1" placeholder="End" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-row">
                <div class="config-item">
                    <div class="page-range-group">
                        <input type="radio" id="rangeSingle" name="pageRange" value="single" />
                        <label for="rangeSingle">Single Page:</label>
                        <input type="number" id="singlePage" value="1" min="1" step="1" placeholder="Page #" />
                    </div>
                </div>
            </div>

            <!-- Capture Settings Row -->
            <div class="config-row">
                <div class="config-item">
                    <label>Capture Delay:</label>
                    <select id="captureDelay">
                        <option value="1000">1 second</option>
                        <option value="2000">2 seconds</option>
                        <option value="3000" selected>3 seconds</option>
                        <option value="5000">5 seconds</option>
                        <option value="8000">8 seconds</option>
                    </select>
                </div>
                <div class="config-item">
                    <label>Quality:</label>
                    <select id="qualitySelect">
                        <option value="screen">Screen Quality</option>
                        <option value="print" selected>Print Quality</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Input for Output PDF -->
        <div class="input-group">
            <label for="outputPath">2. Output PDF:</label>
            <input type="text" id="outputPath" readonly placeholder="Select output PDF location..." />
            <button id="browseOutput">Save As</button>
        </div>

        <!-- Main Action Buttons -->
        <div class="action-buttons">
            <button id="serverButton" disabled>3. Start Server</button>
            <button id="startButton" disabled>4. Start Extraction</button>
            <button id="stopButton" disabled>Stop Capture</button>
        </div>

        <!-- Troubleshooting Section -->
        <div class="troubleshooting-section">
            <h3>🔧 Troubleshooting</h3>
            <button id="cleanupButton">Clean Temp Files</button>
            <p>If extraction fails or gets stuck, click "Clean Temp Files" to remove temporary browser data, then restart the extraction process.</p>
        </div>

        <!-- Status Log -->
        <div id="status-container">
            <h2>Status Log</h2>
            <div id="status">Waiting for instructions...</div>
        </div>
    </div>

    <script src="renderer.js"></script>





</body>
</html>