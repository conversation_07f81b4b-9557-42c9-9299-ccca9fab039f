{"version": 3, "file": "PDFField.js", "sourceRoot": "", "sources": ["../../../src/api/form/PDFField.ts"], "names": [], "mappings": ";;;;AAAA,uEAA8C;AAG9C,oCAA2E;AAC3E,0CAO2B;AAE3B,mCAUkB;AAClB,qCAAwE;AACxE,kCAA0C;AAE1C,4CAAyD;AAgB5C,QAAA,4BAA4B,GAAG,UAC1C,OAAgC;IAEhC,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,CAAC,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvD,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,CAAC,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvD,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/D,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjE,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE,mBAAmB,EAAE;QACzD,CAAC,MAAM,EAAE,OAAO,CAAC;KAClB,CAAC,CAAC;IACH,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,EAAE,yBAAyB,EAAE;QACrE,CAAC,MAAM,EAAE,OAAO,CAAC;KAClB,CAAC,CAAC;IACH,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,qBAAqB,EAAE;QAC7D,CAAC,MAAM,EAAE,OAAO,CAAC;KAClB,CAAC,CAAC;IACH,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3E,yBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;GAmBG;AACH;IAUE,kBACE,SAA0B,EAC1B,GAAW,EACX,GAAgB;QAEhB,gBAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,sBAAe,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACzE,gBAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,aAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,gBAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,qBAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,0BAAO,GAAP;;QACE,aAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,mCAAI,EAAE,CAAC;IACtD,CAAC;IAED;;;;;;;;;;OAUG;IACH,6BAAU,GAAV;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAc,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;;;;OAUG;IACH,iCAAc,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,kCAAe,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;OASG;IACH,6BAAU,GAAV;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAc,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACH,iCAAc,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,kCAAe,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;OAUG;IACH,6BAAU,GAAV;QACE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAc,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,kCAAe,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;OAOG;IACH,mCAAgB,GAAhB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,cAAc;IACd,yCAAsB,GAAtB;QACE,MAAM,IAAI,gCAAyB,CACjC,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED,cAAc;IACd,2CAAwB,GAAxB,UAAyB,KAAc;QACrC,MAAM,IAAI,gCAAyB,CACjC,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAES,8BAAW,GAArB;QACE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAES,8BAAW,GAArB;QACE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAES,0BAAO,GAAjB;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC;IAES,+BAAY,GAAtB,UAAuB,OAatB;;QACC,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAChD,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAM,YAAY,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,IAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;QACpB,IAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;QACpB,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC;QAC1C,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAC5C,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvC,IAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;QAE7B,sBAAc,CAAC,YAAY,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;QAEjD,iCAAiC;QACjC,IAAM,MAAM,GAAG,0BAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtE,wBAAwB;QACxB,IAAM,IAAI,GAAG,2BAAe,CAC1B,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,EACvB,WAAW,EACX,YAAY,CACb,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,OAAO;YAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElC,IAAM,EAAE,GAAG,MAAM,CAAC,oCAAoC,EAAE,CAAC;QACzD,IAAI,eAAe,EAAE;YACnB,EAAE,CAAC,kBAAkB,CAAC,0BAAiB,CAAC,eAAe,CAAC,CAAC,CAAC;SAC3D;QACD,EAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC7B,IAAI,OAAO;YAAE,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;QACjD,IAAI,WAAW;YAAE,EAAE,CAAC,cAAc,CAAC,0BAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;QAEnE,IAAM,EAAE,GAAG,MAAM,CAAC,sBAAsB,EAAE,CAAC;QAC3C,IAAI,WAAW,KAAK,SAAS;YAAE,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAExD,MAAM,CAAC,SAAS,CAAC,sBAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,SAAS,CAAC,sBAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjD,MAAM,CAAC,SAAS,CAAC,sBAAe,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAEnD,2BAA2B;QAC3B,IAAI,SAAS,EAAE;YACb,IAAM,EAAE,SAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,mCAAI,EAAE,CAAC;YACvD,IAAM,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,wBAAe,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SAC5C;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,iDAA8B,GAAxC,UACE,MAA2B,EAC3B,IAAa,EACb,EAA4D;YAA1D,MAAM,YAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAA;QAExB,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE;YACnC,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;YACzD,QAAQ,EAAE,QAAQ,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;YACzE,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;IAES,8CAA2B,GAArC,UACE,MAA2B,EAC3B,OAAgB,EAChB,EAI+D;YAH7D,MAAM,YAAA,EACN,QAAQ,cAAA,EACR,IAAI,UAAA;QAGN,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE;YACnC,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;YAC1D,QAAQ,EACN,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;YAClE,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC;SAC/D,CAAC,CAAC;IACL,CAAC;IAES,0CAAuB,GAAjC,UACE,MAA2B,EAC3B,EAA+D;YAA7D,MAAM,YAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAA;QAExB,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;SACxC;aAAM;YACL,MAAM,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,IAAI,IAAI,EAAE;YACR,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SAChC;aAAM;YACL,MAAM,CAAC,oBAAoB,EAAE,CAAC;SAC/B;IACH,CAAC;IAED,qCAAqC;IACrC,8CAA8C;IAC9C,0CAA0C;IAC1C,wDAAwD;IACxD,MAAM;IACN,uDAAuD;IAEvD,0CAA0C;IAC1C,wDAAwD;IACxD,MAAM;IACN,yDAAyD;IAEzD,+CAA+C;IAC/C,IAAI;IAEI,yCAAsB,GAA9B,UACE,MAA2B,EAC3B,UAAyB,EACzB,IAAc;;QAEN,IAAA,OAAO,GAAK,IAAI,CAAC,SAAS,CAAC,IAAI,QAAxB,CAAyB;QAClC,IAAA,KAAoB,MAAM,CAAC,YAAY,EAAE,EAAvC,KAAK,WAAA,EAAE,MAAM,YAA0B,CAAC;QAEhD,kCAAkC;QAClC,cAAc;QACd,iCAAiC;QACjC,sDAAsD;QACtD,IAAI;QACJ,WAAW;QAEX,IAAM,SAAS,GAAG,IAAI,IAAI,EAAE,IAAI,YAAI,GAAC,IAAI,CAAC,IAAI,IAAG,IAAI,CAAC,GAAG,KAAE,EAAE,CAAC;QAC9D,IAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,UAAU,EAAE;YAC7C,SAAS,WAAA;YACT,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACxC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACxC,CAAC,CAAC;QACH,IAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG;IACO,8CAA2B,GAArC,UACE,MAA2B,EAC3B,KAAe,EACf,SAAyB;QAEzB,0DAA0D;QAC1D,yEAAyE;QACzE,2EAA2E;QAC3E,wEAAwE;;;QAEhE,IAAA,OAAO,GAAK,IAAI,CAAC,SAAS,CAAC,IAAI,QAAxB,CAAyB;QAExC,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,IAAM,EAAE,GAAG,MAAM,CAAC,4BAA4B,EAAE,CAAC;QACjD,IAAM,EAAE,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAEnC,IAAM,WAAW,SAAG,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,QAAQ,qCAAM,CAAC,CAAC;QACxC,IAAM,QAAQ,GAAG,0BAAc,CAAC,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,WAAW,GAAG,CAAC;QAEnD,IAAM,MAAM,GAAG,0BAAa,uCAAM,SAAS,KAAE,QAAQ,UAAA,IAAG,CAAC;QAEzD,IAAM,GAAG,GAAG,iCAAqB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACvD,IAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAChC,GAAG,CAAC,KAAK,GAAG,WAAW,GAAG,CAAC,EAC3B,GAAG,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,CAC7B,CAAC;QAEF,uDAAuD;QACvD,IAAM,OAAO,GAAG;YACd,CAAC,EAAE,WAAW;YACd,CAAC,EAAE,WAAW;YACd,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,EAAE;YACF,MAAM,EAAE,mBAAO,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,mBAAO,CAAC,CAAC,CAAC;YACjB,KAAK,EAAE,mBAAO,CAAC,CAAC,CAAC;SAClB,CAAC;QAEF,IAAI,SAAS,KAAK,sBAAc,CAAC,MAAM,EAAE;YACvC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACrE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SACxE;aAAM,IAAI,SAAS,KAAK,sBAAc,CAAC,KAAK,EAAE;YAC7C,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC;YACtD,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;SACzD;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAChE,IAAM,UAAU,0BAAO,MAAM,EAAK,sBAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QACjE,YAAY;QAEZ,IAAM,SAAS,GAAG,EAAE,OAAO,YAAI,GAAC,SAAS,IAAG,KAAK,CAAC,GAAG,KAAE,EAAE,CAAC;QAC1D,IAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,UAAU,EAAE;YAC7C,SAAS,WAAA;YACT,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACxC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEO,uCAAoB,GAA5B,UACE,MAA2B,EAC3B,UAAqD,EACrD,OAAgB;QAER,IAAA,OAAO,GAAK,IAAI,CAAC,SAAS,CAAC,IAAI,QAAxB,CAAyB;QAExC,IAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QACvE,IAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACvC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACzC,cAAc,CAAC,GAAG,CAAC,cAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC;QAEpD,OAAO,cAAc,CAAC;IACxB,CAAC;IACH,eAAC;AAAD,CAAC,AArbD,IAqbC"}